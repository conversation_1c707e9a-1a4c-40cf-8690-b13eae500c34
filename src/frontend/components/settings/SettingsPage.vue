<template>
  <div class="px-6 py-8 mx-auto max-w-7xl lg:px-8">
    <div class="mb-8">
      <h1 class="text-2xl font-bold">Settings</h1>
      <p class="mt-2">Manage your account settings and preferences.</p>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Settings Navigation -->
      <div class="lg:col-span-1">
        <nav class="space-y-1">
          <a
            href="#profile"
            @click="activeSection = 'profile'"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'profile'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
          </a>
          <a
            href="#api-keys"
            @click="activeSection = 'api-keys'"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'api-keys'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10zM12 11v2m0 4h.01" />
            </svg>
            API keys
          </a>
          <a
            href="#billing"
            @click="activeSection = 'billing'"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'billing'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Billing
          </a>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-2">

        <!-- Profile Section -->
        <div v-if="activeSection === 'profile'" class="card bg-base-100">
          <div class="card-body">
            <h2 class="card-title mb-6">Profile information</h2>

            <div class="bg-base-200/40 rounded-lg p-6">
              <div class="space-y-4">
                <div>
                  <label class="label">
                    <span class="label-text">Email address</span>
                  </label>
                  <input
                    type="email"
                    :value="user?.email || ''"
                    disabled
                    class="input input-bordered w-full cursor-not-allowed"
                  >
                  <label class="label">
                    <span class="label-text-alt text-xs">Email address cannot be changed at this time.</span>
                  </label>
                </div>

                <div>
                  <label class="label">
                    <span class="label-text">Display name</span>
                  </label>
                  <input
                    type="text"
                    v-model="displayName"
                    placeholder="Enter your display name"
                    class="input input-bordered w-full"
                  >
                </div>

                <div class="pt-4">
                  <button
                    type="button"
                    @click="saveChanges"
                    :disabled="saving"
                    class="btn btn-primary"
                  >
                    {{ saving ? 'Saving...' : 'Save changes' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API Keys Section -->
        <ApiKeysSection v-if="activeSection === 'api-keys'" />

        <!-- Billing Section -->
        <BillingSection v-if="activeSection === 'billing'" />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BillingSection from './BillingSection.vue'
import ApiKeysSection from './ApiKeysSection.vue'

// Props
interface Props {
  user?: {
    email: string
    name?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  user: () => ({ email: '', name: '' })
})

// State
const activeSection = ref('profile')
const displayName = ref(props.user?.name || '')
const saving = ref(false)

// Methods
const saveChanges = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving changes:', { displayName: displayName.value })

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message (could be enhanced with toast notifications)
    alert('Changes saved successfully!')
  } catch (error) {
    console.error('Failed to save changes:', error)
    alert('Failed to save changes. Please try again.')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  // Initialize display name from user prop
  displayName.value = props.user?.name || ''
})
</script>

<style scoped>
/* Settings page specific styles */
</style>

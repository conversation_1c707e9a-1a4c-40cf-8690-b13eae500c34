<template>
  <div class="w-full max-w-md mx-auto px-4">
    <!-- Main Card -->
    <div class="card bg-base-100 shadow-2xl border border-base-300/50 backdrop-blur-sm">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h2 class="text-3xl font-bold text-base-content mb-2">
            Welcome back
          </h2>
          <p class="text-base-content/70">
            Sign in to your account to continue
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Login Form -->
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-5">
            <div class="form-control">
              <label for="email" class="label">
                <span class="label-text font-medium">Email address</span>
              </label>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your email address"
              >
            </div>
            <div class="form-control">
              <label for="password" class="label">
                <span class="label-text font-medium">Password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your password"
              >
            </div>
          </div>

          <div class="flex items-center justify-between py-2">
            <div class="form-control">
              <label class="label cursor-pointer justify-start p-0">
                <input
                  id="remember-me"
                  v-model="form.rememberMe"
                  name="remember-me"
                  type="checkbox"
                  class="checkbox checkbox-primary checkbox-sm"
                >
                <span class="label-text ml-3">Remember me</span>
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="link link-primary hover:link-primary/80 transition-colors">
                Forgot password?
              </a>
            </div>
          </div>

          <div class="pt-2">
            <button
              type="submit"
              :disabled="loading"
              class="btn btn-primary w-full h-12 text-base font-medium transition-all hover:scale-[1.02] active:scale-[0.98]"
              :class="{ 'loading': loading }"
            >
              <span v-if="loading" class="loading loading-spinner loading-sm"></span>
              {{ loading ? 'Signing in...' : 'Sign in' }}
            </button>
          </div>
        </form>

        <!-- Sign up link -->
        <div class="divider text-base-content/50">or</div>
        <div class="text-center">
          <p class="text-base-content/70">
            Don't have an account?
            <router-link to="/register" class="link link-primary font-medium hover:link-primary/80 transition-colors">
              Create one here
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// State
const loading = ref(false)
const error = ref('')

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  error.value = ''

  try {
    const response = await fetch('/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies to receive user_token
      body: JSON.stringify({
        email: form.email,
        password: form.password
      }),
    })
    
    const result = await response.json()
    
    if (response.ok) {
      // Success - redirect to dashboard
      // Authentication token is securely stored in httpOnly cookie
      window.location.href = '/domains'
    } else {
      // Show error
      error.value = result.error || 'Login failed. Please try again.'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Login page specific styles */
.card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth focus transitions */
.input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
.btn:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Icon animation */
.card-body svg {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>

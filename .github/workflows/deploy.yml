name: Build and Deploy EU Email Webhook

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Test and Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run type check
        run: npx tsc --noEmit

      - name: Check if tests should be skipped
        id: check-skip-tests
        run: |
          if git log -1 --pretty=%B | grep -q '\[NOTEST\]'; then
            echo "skip=true" >> $GITHUB_OUTPUT
            echo "⏭️ Tests skipped due to [NOTEST] in commit message"
          else
            echo "skip=false" >> $GITHUB_OUTPUT
            echo "🧪 Tests will be executed"
          fi

      # - name: Setup test environment
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   run: |
      #     echo "🔧 Setting up test environment..."
      #     # Create test environment file
      #     cp .env.example .env.test || echo "No .env.example found, using defaults"

      #     # Set test-specific environment variables
      #     echo "NODE_ENV=test" >> .env.test
      #     echo "DATABASE_URL=postgresql://postgres:password@localhost:5432/eu_email_webhook_test" >> .env.test
      #     echo "REDIS_URL=redis://localhost:6379/1" >> .env.test
      #     echo "USER_JWT_SECRET=test-user-jwt-secret" >> .env.test
      #     echo "ADMIN_JWT_SECRET=test-admin-jwt-secret" >> .env.test
      #     echo "ADMIN_PASSWORD=test-admin-password" >> .env.test

      # - name: Start test services
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   run: |
      #     echo "🚀 Starting test services..."
      #     # Start PostgreSQL for tests
      #     sudo systemctl start postgresql
      #     sudo -u postgres createdb eu_email_webhook_test || echo "Test database may already exist"

      #     # Start Redis for tests
      #     sudo systemctl start redis-server

      # - name: Run unit tests
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   env:
      #     CI: true
      #   run: |
      #     echo "🧪 Running unit tests..."
      #     npm run test:unit

      # - name: Run integration tests
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   env:
      #     CI: true
      #   run: |
      #     echo "🧪 Running integration tests..."
      #     npm run test:integration

      # - name: Run webhook tests
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   env:
      #     CI: true
      #   run: |
      #     echo "🧪 Running webhook-specific tests..."
      #     npm run test:webhook

      # - name: Run frontend tests
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   env:
      #     CI: true
      #   run: |
      #     echo "🧪 Running frontend tests..."
      #     npm run test tests/frontend

      # - name: Generate test coverage report
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   env:
      #     CI: true
      #   run: |
      #     echo "📊 Generating test coverage report..."
      #     npm run test:coverage
      #     echo "✅ Test coverage report generated"

      # - name: Upload test coverage reports
      #   if: steps.check-skip-tests.outputs.skip == 'false'
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: test-coverage-reports
      #     path: |
      #       coverage/
      #       junit.xml
      #     retention-days: 30

      # - name: Test summary
      #   if: always()
      #   run: |
      #     if [ "${{ steps.check-skip-tests.outputs.skip }}" == "true" ]; then
      #       echo "⏭️ Tests were skipped due to [NOTEST] in commit message"
      #       echo "🚨 Remember to run tests locally before deploying to production"
      #     else
      #       echo "✅ All test suites completed successfully!"
      #       echo "📊 Test coverage report available in artifacts"
      #       echo "🎯 Unit tests, integration tests, webhook tests, and frontend tests all passed"
      #     fi

  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix=sha-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push main application
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production

      - name: Build and push postfix-manager service
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./postfix-manager-service/Dockerfile
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/postfix-manager:sha-${{ github.sha }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/postfix-manager:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code for validation
        uses: actions/checkout@v4

      - name: Deploy to production server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            #!/bin/bash
            set -e

            echo "🔧 Setting up production environment..."

            # Detect Docker Compose version
            if docker compose version &>/dev/null; then
              DOCKER_COMPOSE="docker compose"
            elif command -v docker-compose &>/dev/null; then
              DOCKER_COMPOSE="docker-compose"
            else
              echo "❌ No supported Docker Compose found" >&2
              exit 1
            fi

            # Ensure user can access docker
            if ! groups | grep -q docker; then
              echo "Adding user to docker group..."
              sudo usermod -aG docker $USER
              echo "Please log out and back in for docker group changes to take effect"
            fi

            # Create and setup application directory
            DEPLOY_DIR=/opt/eu-email-webhook
            REPO_URL=https://${GHCR_TOKEN}@github.com/xadi-hq/eu-email-webhook.git

            # Expects $DEPLOY_DIR to exist (mkdir) and be writable (chown) by the user
            if [ ! -d "$DEPLOY_DIR/.git" ]; then
              echo "📁 Cloning fresh repo into $DEPLOY_DIR..."
              git clone "$REPO_URL" "$DEPLOY_DIR"
            else
              echo "📁 Updating application code at $DEPLOY_DIR..."
              cd "$DEPLOY_DIR"
              git fetch origin
              git reset --hard origin/main
            fi

            cd "$DEPLOY_DIR"

            # Create production environment file
            if [ ! -f ".env.prod" ]; then
              echo "⚙️ Creating production environment file..."
              if [ -f ".env.prod.example" ]; then
                cp .env.prod.example .env.prod
                # Generate secure credentials
                DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
                JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
                sed -i "s|DB_PASSWORD=.*|DB_PASSWORD=$DB_PASSWORD|" .env.prod
                sed -i "s|JWT_SECRET=.*|JWT_SECRET=$JWT_SECRET|" .env.prod
                echo "📝 Generated secure database password and JWT secret"
              else
                echo "Creating minimal production environment..."
                cat > .env.prod << 'EOF'
            NODE_ENV=production
            DB_USER=postgres
            DB_NAME=eu_email_webhook
            DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
            JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
            DATABASE_URL=************************************************/eu_email_webhook
            REDIS_URL=redis://redis:6379
            EOF
              fi
            fi

            # Login to GitHub Container Registry
            echo "${{ secrets.GHCR_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            echo "🔄 Pulling latest production images..."
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/postfix-manager:latest

            echo "🔄 Stopping conflicting services..."
            # Stop any conflicting systemd services
            sudo systemctl stop eu-email-webhook.service 2>/dev/null || echo "No eu-email-webhook service"
            sudo systemctl stop postfix-manager.service 2>/dev/null || echo "No postfix-manager service"

            echo "🔄 Updating production deployment..."
            export IMAGE_TAG=latest
            export POSTFIX_TAG=latest

            # Stop existing containers
            $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod down --timeout 30 --remove-orphans || echo "No existing containers"

            # Start new deployment
            $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod up -d

            echo "⏳ Waiting for services to start..."
            sleep 60

            echo "🏥 Running health checks..."

            # Main application health check with retries
            RETRY_COUNT=0
            MAX_RETRIES=12
            while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
              if curl -f --max-time 10 http://localhost:3000/health; then
                echo "✅ Main application is healthy"
                break
              else
                echo "⏳ Waiting for application... (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)"
                sleep 10
                RETRY_COUNT=$((RETRY_COUNT + 1))
              fi
            done

            if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
              echo "❌ Main application health check failed"
              echo "🔍 Container logs:"
              $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod logs --tail=50 app
              exit 1
            fi

            # Postfix manager health check (non-critical)
            if curl -f --max-time 10 http://localhost:3001/health; then
              echo "✅ Postfix manager is healthy"
            else
              echo "⚠️ Postfix manager health check failed (service may still be functional)"
              $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod logs --tail=20 postfix-manager
            fi

            echo "✅ Deployment completed successfully"
            echo "🎉 EU Email Webhook Service is running"

            # Log successful deployment (without sudo)
            echo "$(date): Deployed ${{ github.sha }} to production" >> /tmp/deployments.log
            cat /tmp/deployments.log | sudo tee -a /var/log/deployments.log > /dev/null || echo "Logged to /tmp/deployments.log"

      - name: Setup Node.js for validation
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies for validation
        run: npm ci

      - name: Post-Deployment E2E Validation
        run: |
          echo "🧪 Validating newly deployed system..."
          if npm run test:e2e; then
            echo "✅ E2E validation passed - deployment fully successful!"
          else
            echo "⚠️ E2E validation failed - deployment succeeded but system may need attention"
            echo "🔍 This is a warning, not a failure. Investigate the E2E test results."
            # Don't exit with error - deployment was successful
          fi
        env:
          API_BASE: https://mw.xadi.eu
          API_KEY: ${{ secrets.JWT_SECRET }}


      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Production deployment completed successfully"
            echo "🎉 New version deployed and running"
            echo "📊 Check E2E validation results above for system health"
          else
            echo "❌ Production deployment failed"
            echo "🔍 Check deployment logs above"
          fi

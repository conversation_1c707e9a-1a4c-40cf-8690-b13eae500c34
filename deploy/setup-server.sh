#!/bin/bash

# Server deployment script for EU Email Webhook Service
# Updated for /opt/eu-email-webhook standardization and Docker-based deployment

set -e

APP_DIR="/opt/eu-email-webhook"
REPO_URL="**************:xadi-hq/eu-email-webhook.git"
SYSTEM_USER="www-data"  # Standard web server user

echo "🚀 Deploying EU Email Webhook Service to production server"
echo "Server: $(hostname -I | awk '{print $1}')"
echo "User: $(whoami)"
echo ""

# 1. Create application directory and set permissions
echo "📂 Setting up application directory..."
sudo mkdir -p $APP_DIR
sudo chown -R $SYSTEM_USER:$SYSTEM_USER $APP_DIR

# Clone repository if not exists
if [ ! -d "$APP_DIR/.git" ]; then
    echo "📂 Cloning repository..."
    sudo -u $SYSTEM_USER git clone $REPO_URL $APP_DIR
else
    echo "📂 Updating repository..."
    cd $APP_DIR
    sudo -u $SYSTEM_USER git pull origin main
fi

cd $APP_DIR
echo "✅ Repository updated"
echo ""

# 2. Install system dependencies
echo "📦 Installing system dependencies..."
sudo apt update
sudo apt install -y \
    postfix \
    postfix-sqlite \
    docker.io \
    curl \
    jq \
    nginx \
    certbot \
    python3-certbot-nginx \
    sqlite3

echo "✅ System dependencies installed"
echo ""

# 3. Configure Docker permissions
echo "🐳 Setting up Docker..."
sudo usermod -aG docker $SYSTEM_USER
sudo systemctl enable docker
sudo systemctl start docker

echo "✅ Docker configured"
echo ""

# 4. Set up environment configuration
if [ ! -f "$APP_DIR/.env.prod" ]; then
    echo "⚙️  Creating production environment configuration..."
    sudo -u $SYSTEM_USER cp $APP_DIR/.env.prod.example $APP_DIR/.env.prod
    
    # Generate secure JWT secret
    JWT_SECRET=$(openssl rand -base64 32)
    sudo -u $SYSTEM_USER sed -i "s|your-super-secret-jwt-key|$JWT_SECRET|g" $APP_DIR/.env.prod
    
    echo "✅ Environment configuration created"
    echo "📝 Please review and update $APP_DIR/.env.prod with your specific values"
else
    echo "⚙️  Environment configuration already exists"
fi

# 5. Create data directories with proper permissions
echo "📁 Creating data directories..."
sudo mkdir -p /opt/eu-email-webhook/data
sudo mkdir -p /opt/eu-email-webhook/scripts
sudo mkdir -p /opt/eu-email-webhook/backups
sudo chown -R $SYSTEM_USER:$SYSTEM_USER /opt/eu-email-webhook

echo "✅ Data directories created"
echo ""

# 6. Build and start Docker containers
echo "🐳 Building and starting Docker containers..."
cd $APP_DIR
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml build
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml up -d

# Initialize scripts volume
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml --profile init up script-init

echo "✅ Docker containers started"
echo ""

# 7. Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw allow 22    # SSH
sudo ufw allow 25    # SMTP
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
# Note: Internal API ports (3000, 3001) stay internal

echo "✅ Firewall configured"
echo ""

# 8. Set up Nginx reverse proxy
echo "🌐 Setting up Nginx..."
sudo tee /etc/nginx/sites-available/eu-email-webhook > /dev/null <<EOF
server {
    listen 80;
    server_name $(hostname -I | awk '{print $1}') mw.xadi.eu;
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        access_log off;
    }

    # Proxy all other requests to the Fastify app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/eu-email-webhook /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

echo "✅ Nginx configured"
echo ""

# 9. Set up Postfix for SQLite (basic configuration)
echo "📧 Configuring Postfix for SQLite support..."
sudo postconf -e "virtual_alias_domains = sqlite:/opt/eu-email-webhook/data/virtual_domains.cf"
sudo postconf -e "virtual_alias_maps = sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf"

# Create basic alias
echo "process-email: \"|/opt/eu-email-webhook/scripts/production/process-email.js\"" | sudo tee -a /etc/aliases > /dev/null
sudo newaliases

# Wait for Docker containers to initialize the SQLite database
echo "⏳ Waiting for SQLite database initialization..."
sleep 10

# Add web.xadi.eu domain to SQLite database for test webhook feature
echo "📧 Adding web.xadi.eu domain for test webhook feature..."
if [ -f "/opt/eu-email-webhook/data/postfix.db" ]; then
    sqlite3 /opt/eu-email-webhook/data/postfix.db "INSERT OR IGNORE INTO virtual_domains (domain, destination, active) VALUES ('web.xadi.eu', 'process-email', 1);"
    echo "✅ web.xadi.eu domain added to SQLite database"
else
    echo "⚠️  SQLite database not found yet - you may need to add web.xadi.eu manually later"
fi

sudo systemctl reload postfix
echo "✅ Postfix configured"
echo ""

# 10. Status check
echo "📊 Service Status:"
echo ""
echo "Docker Containers:"
cd $APP_DIR
sudo -u $SYSTEM_USER docker compose -f docker-compose.prod.yml ps
echo ""
echo "Postfix:"
sudo systemctl status postfix.service --no-pager -l | head -10
echo ""
echo "Nginx:"
sudo systemctl status nginx.service --no-pager -l | head -5

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "🔗 API Endpoints:"
echo "  Health: http://$(hostname -I | awk '{print $1}')/health"
echo "  API: http://$(hostname -I | awk '{print $1}')/api/"
echo ""
echo "📝 Logs:"
echo "  App: docker compose -f $APP_DIR/docker-compose.prod.yml logs -f app"
echo "  Postfix Manager: docker compose -f $APP_DIR/docker-compose.prod.yml logs -f postfix-manager"
echo "  Postfix: journalctl -u postfix.service -f"
echo ""
echo "🧪 Test deployment:"
echo "  curl http://$(hostname -I | awk '{print $1}')/health"
echo ""
echo "📝 Next steps:"
echo "  1. Update $APP_DIR/.env.prod with your specific configuration"
echo "  2. Configure your domain's MX record to point to this server"
echo "  3. Set up SSL certificate with certbot"
echo "  4. Test domain addition via API"
echo "  5. Verify test webhook domain: sqlite3 /opt/eu-email-webhook/data/postfix.db \"SELECT * FROM virtual_domains WHERE domain='web.xadi.eu';\""
